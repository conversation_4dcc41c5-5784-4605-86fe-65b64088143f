import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'reserve_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'widgets/enhanced_gallery_viewer.dart';
import 'widgets/place_video_player.dart';
import 'widgets/shimmer_loading.dart';
import 'widgets/place_title_section.dart';
import 'widgets/place_quick_stats.dart';
import 'widgets/place_host_section.dart';
import 'widgets/place_description_section.dart';
import 'widgets/place_amenities_section.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:share_plus/share_plus.dart';

class PlaceDetailsScreen extends StatefulWidget {
  final Map<String, dynamic>? placeData;
  final int? placeId;
  final PlaceDetailModel? placeDetail;

  const PlaceDetailsScreen({
    super.key,
    this.placeData,
    this.placeId,
    this.placeDetail,
  }) : assert(placeData != null || placeId != null || placeDetail != null,
            'Either placeData, placeId, or placeDetail must be provided');

  @override
  State<PlaceDetailsScreen> createState() => _PlaceDetailsScreenState();
}

class _PlaceDetailsScreenState extends State<PlaceDetailsScreen>
    with TickerProviderStateMixin {
  bool isFavorite = false;
  bool showFullDescription = false;
  PlaceDetailModel? _placeDetail;
  bool _isLoading = false;
  String? _error;

  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeData();
  }

  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeAnimationController.forward();
    _slideAnimationController.forward();
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  void _initializeData() {
    if (widget.placeDetail != null) {
      _placeDetail = widget.placeDetail;
      isFavorite = _placeDetail!.favorite;
    } else if (widget.placeId != null) {
      _loadPlaceDetails();
    } else if (widget.placeData != null) {
      // Convert legacy placeData to PlaceDetailModel for compatibility
      _convertLegacyData();
    }
  }

  Future<void> _loadPlaceDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final apiService = getIt<PropertiesApiService>();
      final placeDetail = await apiService.getPlaceDetails(widget.placeId!);
      setState(() {
        _placeDetail = placeDetail;
        isFavorite = placeDetail.favorite;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _convertLegacyData() {
    // Convert legacy Map data to PlaceDetailModel for backward compatibility
    final data = widget.placeData!;
    _placeDetail = PlaceDetailModel(
      id: data['id'] ?? 0,
      cityId: data['city_id'] ?? 0,
      title: data['title'] ?? '',
      content: data['description'] ?? data['content'] ?? '',
      image: data['image'],
      video: data['video'],
      price: double.tryParse(data['price']?.toString() ?? '0') ?? 0.0,
      weekendPrice: data['weekend_price'] != null
          ? double.tryParse(data['weekend_price'].toString())
          : null,
      weekPrice: data['week_price'] != null
          ? double.tryParse(data['week_price'].toString())
          : null,
      monthPrice: data['month_price'] != null
          ? double.tryParse(data['month_price'].toString())
          : null,
      lat: data['lat'] != null ? double.tryParse(data['lat'].toString()) : null,
      lon: data['lon'] != null ? double.tryParse(data['lon'].toString()) : null,
      confirmation: data['confirmation'] ?? 0,
      active: data['active'] == 1 || data['active'] == true,
      serviceCategoryId: data['service_category_id'] ?? 0,
      serviceCategoryFormId: data['service_category_form_id'],
      userId: data['user_id'] ?? 0,
      views: data['views'] ?? 0,
      createdAt: data['created_at'] ?? '',
      updatedAt: data['updated_at'] ?? '',
      deletedAt: data['deleted_at'],
      rating: data['rating'] != null || data['reviews'] != null
          ? double.tryParse(
              (data['rating'] ?? data['reviews'])?.toString() ?? '0')
          : null,
      noOfRates: data['no_of_rates'],
      noGuests: data['no_guests'] ?? data['guests'],
      beds: data['beds'] ?? data['bedrooms'],
      baths: data['baths'] ?? data['bathrooms'],
      bookingRules: data['booking_rules'],
      cancelationRules: data['cancelation_rules'],
      includeCommissionDaily: data['include_commission_daily'] ?? 0,
      includeCommissionWeekly: data['include_commission_weekly'] ?? 0,
      includeCommissionMonthly: data['include_commission_monthly'] ?? 0,
      favorite: data['favorite'] ?? false,
      gallery: (data['gallery'] as List<dynamic>?)
              ?.map((item) => GalleryItemModel.fromJson(item))
              .toList() ??
          [],
      facilities: (data['facilities'] as List<dynamic>?)
              ?.map((item) => FacilityItemModel.fromJson(item))
              .toList() ??
          [],
      country: data['country'] ?? '',
      city: data['city'] ?? '',
      url: data['url'] ?? '',
      hoster: HosterModel.fromJson(data['hoster'] ?? {}),
      tourismPermitNumber: data['tourism_permit_number'],
      ratings: data['ratings'] != null
          ? (data['ratings'] as List<dynamic>?)
                  ?.map((item) => RatingModel.fromJson(item))
                  .toList() ??
              []
          : [],
    );
    isFavorite = _placeDetail!.favorite;
  }

  String getValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url == 'null') {
      return 'https://placehold.co/400x300';
    }
    return url;
  }

  Widget _buildAnimatedSection(Widget child, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + (index * 100)),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  void _showGallery(BuildContext context, {int initialIndex = 0}) {
    debugPrint('Gallery tap detected');
    final images = _placeDetail!.gallery
        .map((item) => item.image)
        .where((img) => img.isNotEmpty && img != 'null')
        .toList();
    debugPrint('Gallery images: ' + images.toString());
    if (images.isEmpty && (_placeDetail!.image?.isNotEmpty ?? false)) {
      images.add(_placeDetail!.image!);
      debugPrint('Fallback to main image: ' + _placeDetail!.image!);
    }
    if (images.isEmpty) {
      debugPrint('No images to show in gallery.');
      return;
    }
    debugPrint('Opening EnhancedGalleryViewer...');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedGalleryViewer(
          images: images,
          initialIndex: initialIndex,
          heroTag: 'place_image_$initialIndex',
          placeUrl: _placeDetail?.url,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    if (_isLoading) {
      return const PlaceDetailsShimmer();
    }

    if (_error != null) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline,
                  size: 64, color: context.secondaryTextColor),
              const SizedBox(height: 16),
              Text(
                s.dataLoadError,
                style: AppTextStyles.font18Bold
                    .copyWith(color: context.primaryTextColor),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: AppTextStyles.font14Regular
                    .copyWith(color: context.secondaryTextColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  if (widget.placeId != null) {
                    _loadPlaceDetails();
                  }
                },
                child: Text(s.retry),
              ),
            ],
          ),
        ),
      );
    }

    if (_placeDetail == null) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(child: Text(s.noDataAvailable)),
      );
    }

    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // Enhanced Hero Image Section with App Bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: context.backgroundColor,
            elevation: 0,
            systemOverlayStyle: context.isDarkMode
                ? SystemUiOverlayStyle.light
                : SystemUiOverlayStyle.dark,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.cardColor.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios_rounded,
                  color: context.primaryTextColor,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.share_rounded,
                    color: context.primaryTextColor,
                  ),
                  onPressed: () {
                    final url = _placeDetail?.url;
                    if (url != null && url.isNotEmpty) {
                      Share.share(url);
                    }
                  },
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    isFavorite
                        ? Icons.favorite_rounded
                        : Icons.favorite_border_rounded,
                    color: isFavorite ? Colors.red : context.primaryTextColor,
                  ),
                  onPressed: () {
                    setState(() {
                      isFavorite = !isFavorite;
                    });
                  },
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Only wrap the image with GestureDetector
                  Hero(
                    tag: 'place_image_0',
                    child: GestureDetector(
                      onTap: () {
                        debugPrint('Gallery image tapped');
                        _showGallery(context);
                      },
                      child: Image.network(
                        getValidImageUrl(_placeDetail!.gallery.isNotEmpty
                            ? _placeDetail!.gallery.first.image
                            : _placeDetail!.image),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color:
                              context.secondaryTextColor.withValues(alpha: 0.1),
                          child: Icon(
                            Icons.image_not_supported_rounded,
                            size: 64,
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Gallery indicator
                  if (_placeDetail!.gallery.length > 1)
                    Positioned(
                      bottom: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.photo_library_rounded,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${_placeDetail!.gallery.length}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Enhanced Content with Animations
          SliverToBoxAdapter(
            child: AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Container(
                      color: context.backgroundColor,
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Enhanced Title and Location
                            _buildAnimatedSection(
                                PlaceTitleSection(placeDetail: _placeDetail!),
                                0),
                            const SizedBox(height: 24),

                            // Video Player Section
                            if (_placeDetail!.video != null &&
                                _placeDetail!.video!.isNotEmpty)
                              _buildAnimatedSection(_buildVideoSection(s), 1),
                            if (_placeDetail!.video != null &&
                                _placeDetail!.video!.isNotEmpty)
                              const SizedBox(height: 24),

                            // Enhanced Quick Stats
                            _buildAnimatedSection(
                                PlaceQuickStats(placeDetail: _placeDetail!), 2),
                            const SizedBox(height: 24),

                            // Enhanced Host Information
                            _buildAnimatedSection(
                                PlaceHostSection(hoster: _placeDetail!.hoster),
                                3),
                            const SizedBox(height: 24),

                            // Enhanced Description
                            _buildAnimatedSection(
                                PlaceDescriptionSection(
                                    content: _placeDetail!.content),
                                4),
                            const SizedBox(height: 24),

                            // Enhanced Amenities
                            _buildAnimatedSection(
                                PlaceAmenitiesSection(
                                    facilities: _placeDetail!.facilities),
                                5),
                            const SizedBox(height: 24),

                            // Enhanced Location
                            _buildAnimatedSection(_buildLocationSection(s), 6),
                            const SizedBox(height: 24),

                            // Enhanced Reviews
                            _buildAnimatedSection(_buildReviewsSection(s), 7),
                            const SizedBox(
                                height: 100), // Space for bottom booking bar
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBookingBottomBar(s),
    );
  }

  Widget _buildVideoSection(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.play_circle_outline_rounded,
                size: 24,
                color: context.accentColor,
              ),
              const SizedBox(width: 8),
              Text(
                s.video,
                style: AppTextStyles.font20Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          PlaceVideoPlayer(
            videoUrl: _placeDetail!.video!,
            autoPlay: false,
            showControls: true,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.propertyDetails,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              if (_placeDetail!.beds != null)
                _buildStatItem(
                    Icons.bed_rounded, '${_placeDetail!.beds}', s.bedrooms),
              if (_placeDetail!.baths != null)
                _buildStatItem(Icons.bathtub_rounded, '${_placeDetail!.baths}',
                    s.bathrooms),
              if (_placeDetail!.noGuests != null)
                _buildStatItem(Icons.people_rounded,
                    '${_placeDetail!.noGuests}', s.guests),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 24,
            color: context.accentColor,
          ),
        ),
        const SizedBox(height: 8),
        if (value.isNotEmpty)
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildHostSection(S s) {
    final hoster = _placeDetail!.hoster;
    final registeredDate = DateTime.tryParse(hoster.registeredSince);
    final yearsHosting = registeredDate != null
        ? DateTime.now().difference(registeredDate).inDays ~/ 365
        : 0;

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 35,
                    backgroundColor: context.accentColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.person_rounded,
                      size: 35,
                      color: context.accentColor,
                    ),
                  ),
                  // Verification badge (show if rating is high)
                  if (hoster.rating != null && hoster.rating! >= 4.5)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${s.hostedBy} ${hoster.name}',
                            style: AppTextStyles.font18Bold.copyWith(
                              color: context.primaryTextColor,
                            ),
                          ),
                        ),
                        if (hoster.rating != null && hoster.rating! > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.yellow.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.star_rounded,
                                  size: 14,
                                  color: AppColors.yellow,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  hoster.rating!.toStringAsFixed(1),
                                  style: AppTextStyles.font12SemiBold.copyWith(
                                    color: AppColors.yellow,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      yearsHosting > 0
                          ? '$yearsHosting ${s.yearsHosting}'
                          : 'مستضيف جديد', // TODO: Add to localization
                      style: AppTextStyles.font14Regular.copyWith(
                        color: context.secondaryTextColor,
                      ),
                    ),
                    if (hoster.totalNoOfRates != null &&
                        hoster.totalNoOfRates! > 0) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${hoster.totalNoOfRates} ${s.reviews}',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Contact buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement contact host functionality
                  },
                  icon: const Icon(Icons.message_rounded, size: 18),
                  label: Text(s.contactHost),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.accentColor,
                    side: BorderSide(color: context.accentColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement call host functionality
                  },
                  icon: const Icon(Icons.phone_rounded, size: 18),
                  label: Text(s.phone),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.accentColor,
                    side: BorderSide(color: context.accentColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection(S s) {
    final description = _placeDetail!.content.isNotEmpty
        ? _placeDetail!.content
        : s.noDescription;
    final shouldShowMore = description.length > 200;
    final displayText = showFullDescription || !shouldShowMore
        ? description
        : '${description.substring(0, 200)}...';

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.aboutThisPlace,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            displayText,
            style: AppTextStyles.font16Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
          if (shouldShowMore) ...[
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () {
                setState(() {
                  showFullDescription = !showFullDescription;
                });
              },
              child: Text(
                showFullDescription ? s.showLess : s.showMore,
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.accentColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAmenitiesSection(S s) {
    final facilities = _placeDetail!.facilities;

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.whatThisPlaceOffers,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          if (facilities.isEmpty)
            Text(
              s.noAmenitiesListed,
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            )
          else
            ...facilities.take(6).map((facility) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: facility.icon != null
                            ? Image.network(
                                facility.icon!,
                                width: 16,
                                height: 16,
                                color: Colors.green,
                                errorBuilder: (context, error, stackTrace) =>
                                    const Icon(Icons.check_rounded,
                                        size: 16, color: Colors.green),
                              )
                            : const Icon(Icons.check_rounded,
                                size: 16, color: Colors.green),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          facility.title,
                          style: AppTextStyles.font16Regular.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ),
                      if (facility.count > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${facility.count}',
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: context.accentColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                )),
          if (facilities.length > 6) ...[
            const SizedBox(height: 16),
            EnhancedButton(
              text: '${s.showAllAmenities} (${facilities.length})',
              onPressed: () {
                // Show all amenities dialog
              },
              isOutlined: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationSection(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.whereYoullBe,
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            "${_placeDetail!.city}, ${_placeDetail!.country}",
            style: AppTextStyles.font16Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildMapWidget(),
        ],
      ),
    );
  }

  Widget _buildMapWidget() {
    // Default location (Riyadh) if no coordinates are available
    final defaultLat = 24.7136;
    final defaultLon = 46.6753;

    final lat = _placeDetail!.lat ?? defaultLat;
    final lon = _placeDetail!.lon ?? defaultLon;

    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.secondaryTextColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: FlutterMap(
          options: MapOptions(
            initialCenter: LatLng(lat, lon),
            initialZoom: 15.0,
            interactionOptions: const InteractionOptions(
              flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
            ),
          ),
          children: [
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'sa.gatherpoint.app',
            ),
            MarkerLayer(
              markers: [
                Marker(
                  point: LatLng(lat, lon),
                  width: 40,
                  height: 40,
                  child: Container(
                    decoration: BoxDecoration(
                      color: context.accentColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewsSection(S s) {
    final ratingsList = _placeDetail!.ratings;
    final rating = ratingsList.isNotEmpty
        ? ratingsList.map((e) => e.value).reduce((a, b) => a + b) /
            ratingsList.length
        : 0.0;
    final noOfRates = ratingsList.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.star, size: 24, color: AppColors.yellow),
            const SizedBox(width: 8),
            Text(
              rating.toStringAsFixed(1),
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '($noOfRates ${s.reviews})',
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Display tourism permit number if it exists
        if (_placeDetail!.tourismPermitNumber != null &&
            _placeDetail!.tourismPermitNumber!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: Row(
              children: [
                const Icon(Icons.confirmation_number,
                    size: 18, color: AppColors.yellow),
                const SizedBox(width: 6),
                Text(
                  '${s.tourismPermitNumber}: ',
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                Text(
                  _placeDetail!.tourismPermitNumber!,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        // List all real reviews
        ...ratingsList.map((review) => Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: Colors.grey[300],
                        child: const Icon(Icons.person,
                            size: 20, color: Colors.grey),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              review.userName,
                              style: AppTextStyles.font16SemiBold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            Text(
                              review.createdAt,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: List.generate(
                            5,
                            (index) => Icon(
                                  index < review.value.round()
                                      ? Icons.star
                                      : Icons.star_border,
                                  size: 16,
                                  color: AppColors.yellow,
                                )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    review.comment,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
           )),
      ],
    );
  }

  Widget _buildBookingBottomBar(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      text:
                          '${_placeDetail!.price.toStringAsFixed(0)} ${s.sar} ',
                      style: AppTextStyles.font20Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                      children: [
                        TextSpan(
                          text: s.night,
                          style: AppTextStyles.font16Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (_placeDetail!.rating != null)
                    Row(
                      children: [
                        const Icon(Icons.star,
                            size: 16, color: AppColors.yellow),
                        const SizedBox(width: 4),
                        Text(
                          _placeDetail!.rating!.toStringAsFixed(1),
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReserveScreen(
                        placeTitle: _placeDetail!.title,
                        policy: _placeDetail!.cancelationRules ?? '',
                        pricePerNight: _placeDetail!.price,
                      ),
                    ),
                  );
                },
                child: Text(
                  s.reserve,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ✅ Widget مخصص للأيقونة والنص
class FeatureIcon extends StatelessWidget {
  final IconData icon;
  final String label;

  const FeatureIcon({super.key, required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, size: 30),
        const SizedBox(height: 8),
        Text(label,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[600],
            )),
      ],
    );
  }
}
